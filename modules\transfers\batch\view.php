<?php
// Start output buffering at the beginning
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Define receipt type conversion function
function convertReceiptTypeToDbFormat($receipt_type) {
    // Define mapping from display format to database format for mr_history table
    // The mr_history.receipt_type column is ENUM('sales_invoice', 'dr', 'ptr', 'or', 'po')
    $receipt_type_map = [
        'Sales Invoice' => 'sales_invoice',
        'Delivery Receipt' => 'dr',
        'Property Transfer Report' => 'ptr',
        'Official Receipt' => 'or',
        'Purchase Order' => 'po',
        // Add lowercase versions for direct matching
        'sales_invoice' => 'sales_invoice',
        'dr' => 'dr',
        'ptr' => 'ptr',
        'or' => 'or',
        'po' => 'po',
        'local mr' => 'dr' // Map "Local MR" to "dr" (Delivery Receipt)
    ];

    // Convert to lowercase for case-insensitive matching
    $receipt_type_lower = strtolower($receipt_type);

    if (isset($receipt_type_map[$receipt_type])) {
        return $receipt_type_map[$receipt_type];
    } elseif (isset($receipt_type_map[$receipt_type_lower])) {
        return $receipt_type_map[$receipt_type_lower];
    } else {
        // Default to dr if not found
        return 'dr';
    }
}

// Define status badge class helper function if not already defined
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'Pending':
            return 'bg-warning text-dark';
        case 'Approved by Logistics':
        case 'Approved by HIMU':
            return 'bg-info text-white';
        case 'Completed':
            return 'bg-success text-white';
        case 'Rejected':
            return 'bg-danger text-white';
        default:
            return 'bg-secondary text-white';
    }
}

// Ensure user is logged in
requireLogin();

// Get batch transfer ID from URL
$batch_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : null;

// If no ID provided, redirect to list
if (empty($batch_id)) {
    header('Location: /choims/modules/transfers/list.php');
    exit();
}

// Get batch transfer details
$batchQuery = "SELECT b.*,
                    l1.location_name AS source_location,
                    l2.location_name AS destination_location,
                    u1.full_name AS initiated_by_name,
                    u2.full_name AS logistics_approval_by_name,
                    u3.full_name AS himu_approval_by_name,
                    u4.full_name AS received_by_name
              FROM batch_transfers b
              JOIN locations l1 ON b.source_location_id = l1.location_id
              JOIN locations l2 ON b.destination_location_id = l2.location_id
              JOIN users u1 ON b.initiated_by = u1.user_id
              LEFT JOIN users u2 ON b.logistics_approval_by = u2.user_id
              LEFT JOIN users u3 ON b.himu_approval_by = u3.user_id
              LEFT JOIN users u4 ON b.received_by = u4.user_id
              WHERE b.batch_id = ?";

$stmt = mysqli_prepare($conn, $batchQuery);
mysqli_stmt_bind_param($stmt, 'i', $batch_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$batch = mysqli_fetch_assoc($result);

// If batch transfer not found, show error
if (!$batch) {
    echo '<div class="container-fluid"><div class="alert alert-danger">Batch transfer not found.</div></div>';
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
    exit();
}

// Check if user has permission to view this transfer
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
$userHasPermission = hasRole('GodMode', 'Superadmin', 'Logistics', 'HIMU') ||
                    $userLocationId == $batch['source_location_id'] ||
                    $userLocationId == $batch['destination_location_id'];

if (!$userHasPermission) {
    echo '<div class="container-fluid"><div class="alert alert-danger">You do not have permission to view this batch transfer.</div></div>';
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
    exit();
}

// Get the assets in this batch
$assetsQuery = "SELECT a.*, fa.asset_id, fa.asset_name, fa.serial_number, sm.sku_code, sm.sku_name, c.category_name
               FROM batch_transfer_assets a
               JOIN fixed_assets fa ON a.asset_id = fa.asset_id
               JOIN sku_master sm ON fa.sku_id = sm.sku_id
               JOIN categories c ON sm.category_id = c.category_id
               WHERE a.batch_id = ?";

$assetsStmt = mysqli_prepare($conn, $assetsQuery);
mysqli_stmt_bind_param($assetsStmt, 'i', $batch_id);
mysqli_stmt_execute($assetsStmt);
$assetsResult = mysqli_stmt_get_result($assetsStmt);
$assets = [];
while ($asset = mysqli_fetch_assoc($assetsResult)) {
    $assets[] = $asset;
}

// Get the inventory items in this batch
$inventoryQuery = "SELECT i.*, ci.inventory_id, ci.current_quantity, sm.sku_code, sm.sku_name, sm.unit_of_measure, c.category_name
                 FROM batch_transfer_inventory i
                 JOIN consumable_inventory ci ON i.inventory_id = ci.inventory_id
                 JOIN sku_master sm ON ci.sku_id = sm.sku_id
                 JOIN categories c ON sm.category_id = c.category_id
                 WHERE i.batch_id = ?";

$inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($inventoryStmt, 'i', $batch_id);
mysqli_stmt_execute($inventoryStmt);
$inventoryResult = mysqli_stmt_get_result($inventoryStmt);
$inventoryItems = [];
while ($item = mysqli_fetch_assoc($inventoryResult)) {
    $inventoryItems[] = $item;
}

// Determine the item type based on what's in the batch
$itemType = !empty($assets) ? 'Fixed Asset' : (!empty($inventoryItems) ? 'Consumable Inventory' : '');

// Process approvals or rejections
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = isset($_POST['action']) ? sanitizeInput($_POST['action']) : '';

    if ($action == 'approve_logistics' && hasRole('Logistics')) {
        mysqli_begin_transaction($conn);

        // Update batch transfer status
        $updateQuery = "UPDATE batch_transfers SET
                       status = 'Approved by Logistics',
                       logistics_approval_by = ?,
                       logistics_approval_date = NOW()
                       WHERE batch_id = ?";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'ii', $_SESSION['user_id'], $batch_id);

        if (mysqli_stmt_execute($updateStmt)) {
            // Create notification for destination users
            $message = "Batch Transfer #{$batch_id} has been approved by Logistics and is ready for pickup.";

            // Create notification for destination location users
            $notificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                SELECT user_id, 'batch_transfer_approved', 'Batch Transfer Approved', ?, 'batch_transfer', ?
                FROM users WHERE location_id = ?";
            $notifyStmt = mysqli_prepare($conn, $notificationSql);
            mysqli_stmt_bind_param($notifyStmt, 'sii', $message, $batch_id, $batch['destination_location_id']);
            mysqli_stmt_execute($notifyStmt);
            mysqli_stmt_close($notifyStmt);

            // If transfer requires HIMU approval, notify HIMU users
            if ($batch['requires_himu_approval']) {
                $himuMessage = "Batch Transfer #{$batch_id} has been approved by Logistics and requires HIMU approval.";
                $himuNotificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                    SELECT user_id, 'batch_transfer_needs_himu', 'HIMU Approval Required', ?, 'batch_transfer', ?
                    FROM users WHERE role = 'HIMU'";
                $himuNotifyStmt = mysqli_prepare($conn, $himuNotificationSql);
                mysqli_stmt_bind_param($himuNotifyStmt, 'si', $himuMessage, $batch_id);
                mysqli_stmt_execute($himuNotifyStmt);
                mysqli_stmt_close($himuNotifyStmt);
            }

            // Log the approval
            createAuditLog(
                $_SESSION['user_id'],
                'Approve',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => $batch['status']]),
                json_encode(['status' => 'Approved by Logistics'])
            );

            // Log to detailed audit system
            $batch_data = [
                'old_status' => $batch['status'],
                'new_status' => 'Approved by Logistics'
            ];
            logBatchTransferAction($conn, $_SESSION['user_id'], 'transfer_approve', $batch_id, $batch_data);

            // Create notification for superadmins
            createAuditNotification(
                $conn,
                $_SESSION['user_id'],
                'Approve',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => 'Approved by Logistics'])
            );

            mysqli_commit($conn);
            header("Location: /choims/modules/transfers/batch/view.php?id={$batch_id}&success=approved");
            exit();
        } else {
            mysqli_rollback($conn);
            $error_message = "Failed to approve batch transfer: " . mysqli_error($conn);
        }
    } elseif ($action == 'approve_himu' && (hasRole('HIMU') || hasRole('Superadmin')) && $batch['status'] == 'Approved by Logistics' && $batch['requires_himu_approval']) {
        mysqli_begin_transaction($conn);

        // Update batch transfer status
        $updateQuery = "UPDATE batch_transfers SET
                       status = 'Approved by HIMU',
                       himu_approval_by = ?,
                       himu_approval_date = NOW()
                       WHERE batch_id = ?";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'ii', $_SESSION['user_id'], $batch_id);

        if (mysqli_stmt_execute($updateStmt)) {
            // Create notification for destination users
            $message = "Batch Transfer #{$batch_id} has been approved by HIMU and is ready for pickup.";

            // Create notification for destination location users
            $notificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                SELECT user_id, 'batch_transfer_approved', 'Batch Transfer Approved by HIMU', ?, 'batch_transfer', ?
                FROM users WHERE location_id = ?";
            $notifyStmt = mysqli_prepare($conn, $notificationSql);
            mysqli_stmt_bind_param($notifyStmt, 'sii', $message, $batch_id, $batch['destination_location_id']);
            mysqli_stmt_execute($notifyStmt);
            mysqli_stmt_close($notifyStmt);

            // Log the approval
            createAuditLog(
                $_SESSION['user_id'],
                'Approve',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => $batch['status']]),
                json_encode(['status' => 'Approved by HIMU'])
            );

            // Log to detailed audit system
            $batch_data = [
                'old_status' => $batch['status'],
                'new_status' => 'Approved by HIMU'
            ];
            logBatchTransferAction($conn, $_SESSION['user_id'], 'transfer_approve', $batch_id, $batch_data);

            // Create notification for superadmins
            createAuditNotification(
                $conn,
                $_SESSION['user_id'],
                'Approve',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => 'Approved by HIMU'])
            );

            mysqli_commit($conn);
            header("Location: /choims/modules/transfers/batch/view.php?id={$batch_id}&success=approved");
            exit();
        } else {
            mysqli_rollback($conn);
            $error_message = "Failed to approve batch transfer: " . mysqli_error($conn);
        }
    } elseif ($action == 'receive' &&
             $userLocationId == $batch['destination_location_id'] &&
             (($batch['status'] == 'Approved by Logistics' && !$batch['requires_himu_approval']) ||
              ($batch['status'] == 'Approved by HIMU' && $batch['requires_himu_approval']))) {

        mysqli_begin_transaction($conn);

        // Update batch transfer status
        $updateQuery = "UPDATE batch_transfers SET
                       status = 'Completed',
                       received_by = ?,
                       received_date = NOW()
                       WHERE batch_id = ?";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'ii', $_SESSION['user_id'], $batch_id);

        if (mysqli_stmt_execute($updateStmt)) {
            // Process each asset transfer
            foreach ($assets as $asset) {
                // Check if the asset is currently "In use" and update to "Available"
                $checkStatusQuery = "SELECT status FROM fixed_assets WHERE asset_id = ?";
                $checkStatusStmt = mysqli_prepare($conn, $checkStatusQuery);
                mysqli_stmt_bind_param($checkStatusStmt, 'i', $asset['asset_id']);
                mysqli_stmt_execute($checkStatusStmt);
                $statusResult = mysqli_stmt_get_result($checkStatusStmt);
                $assetStatus = mysqli_fetch_assoc($statusResult);
                mysqli_stmt_close($checkStatusStmt);

                if ($assetStatus && $assetStatus['status'] === 'In use') {
                    // Update both location and status
                    $updateAssetQuery = "UPDATE fixed_assets SET
                                        current_location_id = ?,
                                        status = 'Available'
                                        WHERE asset_id = ?";
                    $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
                    mysqli_stmt_bind_param($updateAssetStmt, 'ii', $batch['destination_location_id'], $asset['asset_id']);
                    mysqli_stmt_execute($updateAssetStmt);
                    mysqli_stmt_close($updateAssetStmt);

                    // Log the fixed asset location and status change to detailed audit
                    $old_values = [
                        'current_location_id' => $batch['source_location_id'],
                        'location_name' => $batch['source_location'],
                        'status' => 'In use'
                    ];
                    $new_values = [
                        'current_location_id' => $batch['destination_location_id'],
                        'location_name' => $batch['destination_location'],
                        'status' => 'Available'
                    ];
                    logFixedAssetAction($conn, $_SESSION['user_id'], 'update', $asset['asset_id'], $old_values, $new_values);
                } else {
                    // Just update the location
                    $updateAssetQuery = "UPDATE fixed_assets SET
                                        current_location_id = ?
                                        WHERE asset_id = ?";
                    $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
                    mysqli_stmt_bind_param($updateAssetStmt, 'ii', $batch['destination_location_id'], $asset['asset_id']);
                    mysqli_stmt_execute($updateAssetStmt);
                    mysqli_stmt_close($updateAssetStmt);

                    // Log the fixed asset location change to detailed audit
                    $old_values = [
                        'current_location_id' => $batch['source_location_id'],
                        'location_name' => $batch['source_location']
                    ];
                    $new_values = [
                        'current_location_id' => $batch['destination_location_id'],
                        'location_name' => $batch['destination_location']
                    ];
                    logFixedAssetAction($conn, $_SESSION['user_id'], 'update', $asset['asset_id'], $old_values, $new_values);
                }

                // Check if this is a transfer from logistics to department/health center
                // Get source location type
                $sourceTypeQuery = "SELECT location_type FROM locations WHERE location_id = ?";
                $sourceTypeStmt = mysqli_prepare($conn, $sourceTypeQuery);
                mysqli_stmt_bind_param($sourceTypeStmt, 'i', $batch['source_location_id']);
                mysqli_stmt_execute($sourceTypeStmt);
                $sourceTypeResult = mysqli_stmt_get_result($sourceTypeStmt);
                $sourceLocation = mysqli_fetch_assoc($sourceTypeResult);
                mysqli_stmt_close($sourceTypeStmt);

                // Get destination location type
                $destTypeQuery = "SELECT location_type FROM locations WHERE location_id = ?";
                $destTypeStmt = mysqli_prepare($conn, $destTypeQuery);
                mysqli_stmt_bind_param($destTypeStmt, 'i', $batch['destination_location_id']);
                mysqli_stmt_execute($destTypeStmt);
                $destTypeResult = mysqli_stmt_get_result($destTypeStmt);
                $destLocation = mysqli_fetch_assoc($destTypeResult);
                mysqli_stmt_close($destTypeStmt);

                $isLogisticsSource = false;
                if ($batch['source_location_id'] == 5 ||
                    stripos($batch['source_location'], 'PROPERTY AND SUPPLY') !== false ||
                    stripos($batch['source_location'], 'LOGISTIC') !== false ||
                    stripos($sourceLocation['location_type'], 'PROPERTY AND SUPPLY') !== false ||
                    stripos($sourceLocation['location_type'], 'LOGISTIC') !== false) {
                    $isLogisticsSource = true;
                }

                // If transferring FROM logistics TO department/health center, create MR History entry
                if ($isLogisticsSource &&
                    ($destLocation['location_type'] == 'Department' || $destLocation['location_type'] == 'HealthCenter')) {

                    // Get MR information from the fixed asset
                    $assetMRQuery = "
                        SELECT assigned_to, local_mr, receipt_type
                        FROM fixed_assets
                        WHERE asset_id = ?
                    ";
                    $assetMRStmt = mysqli_prepare($conn, $assetMRQuery);
                    mysqli_stmt_bind_param($assetMRStmt, 'i', $asset['asset_id']);
                    mysqli_stmt_execute($assetMRStmt);
                    $assetMRResult = mysqli_stmt_get_result($assetMRStmt);
                    $assetMR = mysqli_fetch_assoc($assetMRResult);
                    mysqli_stmt_close($assetMRStmt);

                    // Create a new MR history entry
                    $mrHistoryQuery = "
                        INSERT INTO mr_history (
                            asset_id, assigned_to, local_mr, receipt_type,
                            transfer_id, created_by, start_date
                        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ";

                    // Convert receipt_type to database format (if exists)
                    $dbReceiptType = null;
                    if (!empty($assetMR['receipt_type'])) {
                        $dbReceiptType = convertReceiptTypeToDbFormat($assetMR['receipt_type']);
                    } else {
                        // Default to 'dr' (Local MR) if nothing else is available
                        $dbReceiptType = 'dr';
                    }

                    $mrHistoryStmt = mysqli_prepare($conn, $mrHistoryQuery);
                    mysqli_stmt_bind_param(
                        $mrHistoryStmt,
                        'isssii',
                        $asset['asset_id'],
                        $assetMR['assigned_to'],
                        $assetMR['local_mr'],
                        $dbReceiptType,
                        $batch_id,
                        $_SESSION['user_id']
                    );
                    mysqli_stmt_execute($mrHistoryStmt);
                    mysqli_stmt_close($mrHistoryStmt);

                    error_log("Created new MR history entry for asset ID: " . $asset['asset_id'] . " in batch transfer when received by department/health center");
                }
            }

            // Process each inventory transfer
            foreach ($inventoryItems as $item) {
                // Check if destination already has this inventory item
                $checkQuery = "SELECT inventory_id, current_quantity FROM consumable_inventory
                              WHERE sku_id = (SELECT sku_id FROM consumable_inventory WHERE inventory_id = ?)
                              AND location_id = ?";
                $checkStmt = mysqli_prepare($conn, $checkQuery);
                mysqli_stmt_bind_param($checkStmt, 'ii', $item['inventory_id'], $batch['destination_location_id']);
                mysqli_stmt_execute($checkStmt);
                $checkResult = mysqli_stmt_get_result($checkStmt);

                if (mysqli_num_rows($checkResult) > 0) {
                    // Update existing inventory
                    $destInventory = mysqli_fetch_assoc($checkResult);
                    $newQuantity = $destInventory['current_quantity'] + $item['quantity'];

                    $updateInvQuery = "UPDATE consumable_inventory SET
                                     current_quantity = ?,
                                     updated_at = NOW()
                                     WHERE inventory_id = ?";
                    $updateInvStmt = mysqli_prepare($conn, $updateInvQuery);
                    mysqli_stmt_bind_param($updateInvStmt, 'ii', $newQuantity, $destInventory['inventory_id']);
                    mysqli_stmt_execute($updateInvStmt);
                    mysqli_stmt_close($updateInvStmt);

                    // Log the inventory quantity change to detailed audit
                    $old_values = [
                        'current_quantity' => $destInventory['current_quantity']
                    ];
                    $new_values = [
                        'current_quantity' => $newQuantity
                    ];
                    logConsumableAction($conn, $_SESSION['user_id'], 'update', $destInventory['inventory_id'], $old_values, $new_values);
                } else {
                    // Create new inventory at destination
                    $createInvQuery = "INSERT INTO consumable_inventory (
                                      sku_id, location_id, current_quantity, min_quantity, status,
                                      low_stock_threshold, critical_threshold, last_restock_date
                                     ) SELECT
                                      sku_id, ?, ?, min_quantity, 'Available',
                                      low_stock_threshold, critical_threshold, NOW()
                                     FROM consumable_inventory
                                     WHERE inventory_id = ?";
                    $createInvStmt = mysqli_prepare($conn, $createInvQuery);
                    mysqli_stmt_bind_param($createInvStmt, 'iii', $batch['destination_location_id'], $item['quantity'], $item['inventory_id']);
                    mysqli_stmt_execute($createInvStmt);
                    $new_inventory_id = mysqli_insert_id($conn);
                    mysqli_stmt_close($createInvStmt);

                    // Get source inventory details for the new inventory
                    $sourceInvQuery = "SELECT * FROM consumable_inventory WHERE inventory_id = ?";
                    $sourceInvStmt = mysqli_prepare($conn, $sourceInvQuery);
                    mysqli_stmt_bind_param($sourceInvStmt, 'i', $item['inventory_id']);
                    mysqli_stmt_execute($sourceInvStmt);
                    $sourceInvResult = mysqli_stmt_get_result($sourceInvStmt);
                    $sourceInv = mysqli_fetch_assoc($sourceInvResult);
                    mysqli_stmt_close($sourceInvStmt);

                    // Log the new inventory creation to detailed audit
                    $new_values = [
                        'sku_id' => $sourceInv['sku_id'],
                        'location_id' => $batch['destination_location_id'],
                        'current_quantity' => $item['quantity'],
                        'min_quantity' => $sourceInv['min_quantity'],
                        'status' => 'Available',
                        'low_stock_threshold' => $sourceInv['low_stock_threshold'],
                        'critical_threshold' => $sourceInv['critical_threshold']
                    ];
                    logConsumableAction($conn, $_SESSION['user_id'], 'create', $new_inventory_id, null, $new_values);
                }

                mysqli_free_result($checkResult);
                mysqli_stmt_close($checkStmt);

                // Reduce quantity at source
                $sourceQuery = "UPDATE consumable_inventory SET
                              current_quantity = current_quantity - ?,
                              updated_at = NOW()
                              WHERE inventory_id = ?";
                $sourceStmt = mysqli_prepare($conn, $sourceQuery);
                mysqli_stmt_bind_param($sourceStmt, 'ii', $item['quantity'], $item['inventory_id']);
                mysqli_stmt_execute($sourceStmt);
                mysqli_stmt_close($sourceStmt);

                // Get source inventory details for logging
                $sourceInvQuery = "SELECT * FROM consumable_inventory WHERE inventory_id = ?";
                $sourceInvStmt = mysqli_prepare($conn, $sourceInvQuery);
                mysqli_stmt_bind_param($sourceInvStmt, 'i', $item['inventory_id']);
                mysqli_stmt_execute($sourceInvStmt);
                $sourceInvResult = mysqli_stmt_get_result($sourceInvStmt);
                $sourceInv = mysqli_fetch_assoc($sourceInvResult);
                mysqli_stmt_close($sourceInvStmt);

                // Log the source inventory quantity change to detailed audit
                $old_values = [
                    'current_quantity' => $sourceInv['current_quantity'] + $item['quantity']
                ];
                $new_values = [
                    'current_quantity' => $sourceInv['current_quantity']
                ];
                logConsumableAction($conn, $_SESSION['user_id'], 'update', $item['inventory_id'], $old_values, $new_values);
            }

            // Log the receipt
            createAuditLog(
                $_SESSION['user_id'],
                'Complete',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => $batch['status']]),
                json_encode(['status' => 'Completed'])
            );

            // Create notification for superadmins
            createAuditNotification(
                $conn,
                $_SESSION['user_id'],
                'Complete',
                'Batch Transfer',
                $batch_id,
                json_encode(['status' => 'Completed'])
            );

            // Log to detailed audit system
            $batch_data = [
                'old_status' => $batch['status'],
                'new_status' => 'Completed'
            ];
            logBatchTransferAction($conn, $_SESSION['user_id'], 'transfer_complete', $batch_id, $batch_data);

            // Create notification for source location users
            $completeMessage = "Batch Transfer #{$batch_id} has been received by " . $_SESSION['username'] . " at " . $batch['destination_location'] . ".";
            $completeNotificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                SELECT user_id, 'batch_transfer_completed', 'Batch Transfer Completed', ?, 'batch_transfer', ?
                FROM users WHERE location_id = ?";
            $completeNotifyStmt = mysqli_prepare($conn, $completeNotificationSql);
            mysqli_stmt_bind_param($completeNotifyStmt, 'sii', $completeMessage, $batch_id, $batch['source_location_id']);
            mysqli_stmt_execute($completeNotifyStmt);
            mysqli_stmt_close($completeNotifyStmt);

            mysqli_commit($conn);

            // If there's only one asset in the transfer, redirect to that asset's view page
            if (count($assets) == 1) {
                $asset_id = $assets[0]['asset_id'];
                $_SESSION['transfer_received'] = true;
                header("Location: /choims/modules/assets/view.php?id={$asset_id}&transfer_received=1");
                exit();
            } else {
                header("Location: /choims/modules/transfers/batch/view.php?id={$batch_id}&success=received");
                exit();
            }
        } else {
            mysqli_rollback($conn);
            $error_message = "Failed to receive batch transfer: " . mysqli_error($conn);
        }
    } elseif ($action == 'reject') {
        $rejection_reason = sanitizeInput($_POST['rejection_reason']);

        if (empty($rejection_reason)) {
            $error_message = "Rejection reason is required.";
        } else {
            mysqli_begin_transaction($conn);

            // Update batch transfer status
            $updateQuery = "UPDATE batch_transfers SET
                           status = 'Rejected',
                           rejection_reason = ?
                           WHERE batch_id = ?";
            $updateStmt = mysqli_prepare($conn, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, 'si', $rejection_reason, $batch_id);

            if (mysqli_stmt_execute($updateStmt)) {
                // Log the rejection
                createAuditLog(
                    $_SESSION['user_id'],
                    'Reject',
                    'Batch Transfer',
                    $batch_id,
                    json_encode(['status' => $batch['status']]),
                    json_encode(['status' => 'Rejected', 'rejection_reason' => $rejection_reason])
                );

                // Log to detailed audit system
                $batch_data = [
                    'old_status' => $batch['status'],
                    'new_status' => 'Rejected',
                    'rejection_reason' => $rejection_reason
                ];
                logBatchTransferAction($conn, $_SESSION['user_id'], 'transfer_reject', $batch_id, $batch_data);

                // Create notification for superadmins
                createAuditNotification(
                    $conn,
                    $_SESSION['user_id'],
                    'Reject',
                    'Batch Transfer',
                    $batch_id,
                    json_encode(['status' => 'Rejected', 'rejection_reason' => $rejection_reason])
                );

                // Create notification for source location users
                $rejectMessageSource = "Batch Transfer #{$batch_id} has been rejected. Reason: " . substr($rejection_reason, 0, 100) . (strlen($rejection_reason) > 100 ? '...' : '');
                $rejectNotificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                    SELECT user_id, 'batch_transfer_rejected', 'Batch Transfer Rejected', ?, 'batch_transfer', ?
                    FROM users WHERE location_id = ?";
                $rejectNotifyStmt = mysqli_prepare($conn, $rejectNotificationSql);
                mysqli_stmt_bind_param($rejectNotifyStmt, 'sii', $rejectMessageSource, $batch_id, $batch['source_location_id']);
                mysqli_stmt_execute($rejectNotifyStmt);
                mysqli_stmt_close($rejectNotifyStmt);

                // Create notification for destination location users
                $rejectMessageDest = "Batch Transfer #{$batch_id} to your location has been rejected. Reason: " . substr($rejection_reason, 0, 100) . (strlen($rejection_reason) > 100 ? '...' : '');
                $rejectDestNotificationSql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                    SELECT user_id, 'batch_transfer_rejected', 'Batch Transfer Rejected', ?, 'batch_transfer', ?
                    FROM users WHERE location_id = ?";
                $rejectDestNotifyStmt = mysqli_prepare($conn, $rejectDestNotificationSql);
                mysqli_stmt_bind_param($rejectDestNotifyStmt, 'sii', $rejectMessageDest, $batch_id, $batch['destination_location_id']);
                mysqli_stmt_execute($rejectDestNotifyStmt);
                mysqli_stmt_close($rejectDestNotifyStmt);

                mysqli_commit($conn);
                header("Location: /choims/modules/transfers/batch/view.php?id={$batch_id}&success=rejected");
                exit();
            } else {
                mysqli_rollback($conn);
                $error_message = "Failed to reject batch transfer: " . mysqli_error($conn);
            }
        }
    }

    // Refresh batch data after action
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $batch = mysqli_fetch_assoc($result);
}

$success_message = '';
if (isset($_GET['success'])) {
    if ($_GET['success'] == 'approved') {
        $success_message = "Batch transfer approved successfully.";
    } elseif ($_GET['success'] == 'received') {
        $success_message = "Batch transfer received successfully.";
    } elseif ($_GET['success'] == 'rejected') {
        $success_message = "Batch transfer rejected.";
    } elseif ($_GET['success'] == '1') {
        $success_message = "Batch transfer initiated successfully.";
    } else {
        $success_message = sanitizeInput($_GET['success']);
    }
}
?>

<div class="container-fluid">
    <div class="modern-page-header mb-4">
        <?php if (!empty($success_message)): ?>
            <div class="success-toast">
                <div class="success-toast-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="success-toast-content">
                    <?php echo $success_message; ?>
                </div>
                <button type="button" class="success-toast-close" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['status_changed_assets']) && $_SESSION['status_changed_assets'] > 0): ?>
            <div class="info-toast">
                <div class="info-toast-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="info-toast-content">
                    <?php echo $_SESSION['status_changed_assets']; ?> asset(s) had their status automatically changed from "In use" to "Available" for this transfer.
                </div>
                <button type="button" class="info-toast-close" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <?php unset($_SESSION['status_changed_assets']); ?>
        <?php endif; ?>

        <div class="d-sm-flex align-items-center justify-content-between">
            <div>
                <h1 class="h3 mb-0">Batch Transfer #<?php echo $batch_id; ?></h1>
                <p class="text-muted mb-0">
                    <span class="status-badge <?php echo strtolower(str_replace(' ', '-', $batch['status'])); ?>">
                        <i class="fas fa-circle me-1"></i> <?php echo $batch['status']; ?>
                    </span>
                    <span class="ms-2"><?php echo formatDateTime($batch['transfer_date']); ?></span>
                </p>
            </div>
            <div class="action-buttons">
                <?php if (hasRole('Logistics') && $batch['status'] == 'Pending'): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="action" value="approve_logistics">
                        <button type="submit" class="btn btn-soft-success">
                            <i class="fas fa-check me-1"></i> Approve Transfer
                        </button>
                    </form>
                <?php endif; ?>

                <?php if ((hasRole('HIMU') || hasRole('Superadmin')) && $batch['status'] == 'Approved by Logistics' && $batch['requires_himu_approval']): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="action" value="approve_himu">
                        <button type="submit" class="btn btn-soft-success">
                            <i class="fas fa-check me-1"></i> HIMU Approval
                        </button>
                    </form>
                <?php endif; ?>

                <?php if ($userLocationId == $batch['destination_location_id'] &&
                         (($batch['status'] == 'Approved by Logistics' && !$batch['requires_himu_approval']) ||
                          ($batch['status'] == 'Approved by HIMU' && $batch['requires_himu_approval']))): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="action" value="receive">
                        <button type="submit" class="btn btn-soft-primary">
                            <i class="fas fa-inbox me-1"></i> Receive Transfer
                        </button>
                    </form>
                <?php endif; ?>

                <a href="/choims/modules/transfers/list.php" class="btn btn-soft-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>

                <?php
                // Check for fixed assets with incomplete MR receipt information to determine PTR button visibility
                $hasFixedAssetsWithIncompleteMR = false;
                foreach ($assets as $asset) {
                    // Get receipt information for this asset from asset_receipts table
                    $assetReceiptQuery = "SELECT receipt_type, local_mr, assigned_to FROM asset_receipts WHERE asset_id = ?";
                    $assetReceiptStmt = mysqli_prepare($conn, $assetReceiptQuery);
                    mysqli_stmt_bind_param($assetReceiptStmt, 'i', $asset['asset_id']);
                    mysqli_stmt_execute($assetReceiptStmt);
                    $assetReceiptResult = mysqli_stmt_get_result($assetReceiptStmt);
                    $assetReceipt = mysqli_fetch_assoc($assetReceiptResult);
                    mysqli_stmt_close($assetReceiptStmt);

                    // Check if this asset has incomplete MR receipt information
                    // Only block PTR if the asset is missing required MR fields
                    if (!$assetReceipt ||
                        empty($assetReceipt['receipt_type']) ||
                        empty($assetReceipt['local_mr']) ||
                        empty($assetReceipt['assigned_to'])) {
                        $hasFixedAssetsWithIncompleteMR = true;
                        break;
                    }
                }

                // Show PTR button for completed transfers or for consumable inventory immediately after initiation
                // BUT only if all fixed assets have complete MR receipt information
                if (($batch['status'] == 'Completed' || !empty($inventoryItems)) && !$hasFixedAssetsWithIncompleteMR):
                ?>
                <button type="button" class="btn btn-soft-success" onclick="printBatchTransferReceipt()">
                    <i class="fas fa-print me-1"></i> Print PTR Receipt
                </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="alert-card error mb-4">
            <div class="alert-card-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-card-content">
                <?php echo $error_message; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Batch Transfer Information -->
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Transfer Information</h6>
            </div>
            <?php if (isset($batch['transaction_code']) && !empty($batch['transaction_code'])): ?>
                <div class="transaction-code">
                    <i class="fas fa-hashtag me-1"></i>
                    <?php echo htmlspecialchars($batch['transaction_code']); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="modern-card-body">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                            <span>Locations</span>
                        </div>
                        <div class="info-card-body">
                            <div class="info-item">
                                <div class="info-label">Source:</div>
                                <div class="info-value">
                                    <div class="location-badge source">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        <?php echo $batch['source_location']; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Destination:</div>
                                <div class="info-value">
                                    <div class="location-badge destination">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        <?php echo $batch['destination_location']; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card">
                        <div class="info-card-header">
                            <i class="fas fa-user-clock me-2 text-primary"></i>
                            <span>People & Timestamps</span>
                        </div>
                        <div class="info-card-body">
                            <div class="info-item">
                                <div class="info-label">Initiated By:</div>
                                <div class="info-value">
                                    <span class="user-badge">
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo $batch['initiated_by_name']; ?>
                                    </span>
                                    <span class="timestamp"><?php echo formatDateTime($batch['transfer_date']); ?></span>
                                </div>
                            </div>

                            <?php if ($batch['logistics_approval_by']): ?>
                                <div class="info-item">
                                    <div class="info-label">Logistics Approval:</div>
                                    <div class="info-value">
                                        <span class="user-badge logistics">
                                            <i class="fas fa-clipboard-check me-1"></i>
                                            <?php echo $batch['logistics_approval_by_name']; ?>
                                        </span>
                                        <span class="timestamp"><?php echo formatDateTime($batch['logistics_approval_date']); ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($batch['himu_approval_by']): ?>
                                <div class="info-item">
                                    <div class="info-label">HIMU Approval:</div>
                                    <div class="info-value">
                                        <span class="user-badge himu">
                                            <i class="fas fa-laptop-medical me-1"></i>
                                            <?php echo $batch['himu_approval_by_name']; ?>
                                        </span>
                                        <span class="timestamp"><?php echo formatDateTime($batch['himu_approval_date']); ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($batch['received_by']): ?>
                                <div class="info-item">
                                    <div class="info-label">Received By:</div>
                                    <div class="info-value">
                                        <span class="user-badge received">
                                            <i class="fas fa-inbox me-1"></i>
                                            <?php echo $batch['received_by_name']; ?>
                                        </span>
                                        <span class="timestamp"><?php echo formatDateTime($batch['received_date']); ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($batch['transfer_notes'])): ?>
                <div class="info-card mt-4">
                    <div class="info-card-header">
                        <i class="fas fa-sticky-note me-2 text-warning"></i>
                        <span>Transfer Notes</span>
                    </div>
                    <div class="info-card-body">
                        <div class="notes-content">
                            <?php echo nl2br(htmlspecialchars($batch['transfer_notes'])); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($batch['rejection_reason'])): ?>
                <div class="info-card mt-4 rejection-card">
                    <div class="info-card-header">
                        <i class="fas fa-times-circle me-2 text-danger"></i>
                        <span>Rejection Reason</span>
                    </div>
                    <div class="info-card-body">
                        <div class="notes-content rejection">
                            <?php echo nl2br(htmlspecialchars($batch['rejection_reason'])); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ((hasRole('Logistics') && $batch['status'] == 'Pending') ||
                  (hasRole('HIMU') && ($batch['status'] == 'Pending' || $batch['status'] == 'Approved by Logistics') && $batch['requires_himu_approval'])): ?>
                <div class="mt-4 text-end">
                    <button type="button" class="btn btn-soft-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                        <i class="fas fa-times me-1"></i> Reject Transfer
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Transfer Timeline -->
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-history"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Transfer Timeline</h6>
            </div>
        </div>
        <div class="modern-card-body">
            <div class="horizontal-timeline">
                <div class="timeline-progress-bar">
                    <div class="progress-track"></div>
                    <div class="progress-fill" style="width: <?php
                        if ($batch['status'] == 'Rejected') {
                            echo '0%';
                        } elseif ($batch['status'] == 'Pending') {
                            echo '25%';
                        } elseif ($batch['status'] == 'Approved by Logistics') {
                            echo '50%';
                        } elseif ($batch['status'] == 'Approved by HIMU') {
                            echo '75%';
                        } elseif ($batch['status'] == 'Completed') {
                            echo '100%';
                        }
                    ?>"></div>
                </div>

                <div class="timeline-steps">
                    <!-- Initiated Step -->
                    <div class="timeline-step <?php echo $batch['status'] != 'Rejected' ? 'completed' : 'rejected'; ?>">
                        <div class="step-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="step-label">Initiated</div>
                        <div class="step-details">
                            <div class="step-date"><?php echo formatDateTime($batch['transfer_date']); ?></div>
                            <div class="step-user"><?php echo $batch['initiated_by_name']; ?></div>
                        </div>
                    </div>

                    <!-- Logistics Approval Step -->
                    <div class="timeline-step <?php echo in_array($batch['status'], ['Approved by Logistics', 'Approved by HIMU', 'Completed']) ? 'completed' : ($batch['status'] == 'Rejected' ? 'rejected' : ''); ?>">
                        <div class="step-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="step-label">Logistics Approval</div>
                        <div class="step-details">
                            <?php if (!empty($batch['logistics_approval_date'])): ?>
                                <div class="step-date"><?php echo formatDateTime($batch['logistics_approval_date']); ?></div>
                                <div class="step-user"><?php echo $batch['logistics_approval_by_name']; ?></div>
                            <?php else: ?>
                                <div class="step-pending">Pending</div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- HIMU Approval Step (if required) -->
                    <?php if ($batch['requires_himu_approval']): ?>
                    <div class="timeline-step <?php echo in_array($batch['status'], ['Approved by HIMU', 'Completed']) ? 'completed' : ($batch['status'] == 'Rejected' ? 'rejected' : ''); ?>">
                        <div class="step-icon">
                            <i class="fas fa-laptop-medical"></i>
                        </div>
                        <div class="step-label">HIMU Approval</div>
                        <div class="step-details">
                            <?php if (!empty($batch['himu_approval_date'])): ?>
                                <div class="step-date"><?php echo formatDateTime($batch['himu_approval_date']); ?></div>
                                <div class="step-user"><?php echo $batch['himu_approval_by_name']; ?></div>
                            <?php else: ?>
                                <div class="step-pending">Pending</div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Completed Step -->
                    <div class="timeline-step <?php echo $batch['status'] == 'Completed' ? 'completed' : ($batch['status'] == 'Rejected' ? 'rejected' : ''); ?>">
                        <div class="step-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="step-label">Completed</div>
                        <div class="step-details">
                            <?php if (!empty($batch['received_date'])): ?>
                                <div class="step-date"><?php echo formatDateTime($batch['received_date']); ?></div>
                                <div class="step-user"><?php echo $batch['received_by_name']; ?></div>
                            <?php else: ?>
                                <div class="step-pending">Pending</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php if ($batch['status'] == 'Rejected'): ?>
                <div class="rejection-banner mt-3">
                    <div class="rejection-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="rejection-content">
                        <div class="rejection-title">Transfer Rejected</div>
                        <div class="rejection-reason">
                            <?php echo htmlspecialchars($batch['rejection_reason']); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Fixed Assets Section -->
    <?php if (!empty($assets)): ?>
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-laptop"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Fixed Assets</h6>
            </div>
            <div class="badge bg-soft-success"><?php echo count($assets); ?> items</div>
        </div>
        <div class="modern-card-body">
            <div class="table-responsive">
                <table class="modern-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Asset ID</th>
                            <th>SKU Code</th>
                            <th>Item Name</th>
                            <th>Serial Number</th>
                            <th>Category</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($assets as $asset): ?>
                            <tr class="item-row">
                                <td><span class="id-badge"><?php echo $asset['asset_id']; ?></span></td>
                                <td><span class="code-badge"><?php echo $asset['sku_code']; ?></span></td>
                                <td>
                                    <div class="item-name"><?php echo $asset['asset_name']; ?></div>
                                </td>
                                <td><?php echo $asset['serial_number'] ?: '<span class="text-muted">—</span>'; ?></td>
                                <td><span class="category-badge"><?php echo $asset['category_name']; ?></span></td>
                                <td>
                                    <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="btn btn-soft-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Inventory Items Section -->
    <?php if (!empty($inventoryItems)): ?>
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-boxes"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Consumable Inventory</h6>
            </div>
            <div class="badge bg-soft-info"><?php echo count($inventoryItems); ?> items</div>
        </div>
        <div class="modern-card-body">
            <div class="table-responsive">
                <table class="modern-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Inventory ID</th>
                            <th>SKU Code</th>
                            <th>Item Name</th>
                            <th>Category</th>
                            <th>Quantity to Transfer</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($inventoryItems as $item): ?>
                            <tr class="item-row">
                                <td><span class="id-badge"><?php echo $item['inventory_id']; ?></span></td>
                                <td><span class="code-badge"><?php echo $item['sku_code']; ?></span></td>
                                <td>
                                    <div class="item-name"><?php echo $item['sku_name']; ?></div>
                                </td>
                                <td><span class="category-badge"><?php echo $item['category_name']; ?></span></td>
                                <td>
                                    <div class="quantity-info">
                                        <span class="quantity-badge"><?php echo $item['quantity']; ?></span>
                                    </div>
                                </td>
                                <td>
                                    <a href="/choims/modules/inventory/view.php?id=<?php echo $item['inventory_id']; ?>" class="btn btn-soft-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <form method="post">
                <input type="hidden" name="action" value="reject">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">
                        <i class="fas fa-times-circle me-2 text-danger"></i> Reject Batch Transfer
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert-card warning mb-3">
                        <div class="alert-card-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="alert-card-content">
                            Rejecting this transfer will cancel the process and notify all involved parties. This action cannot be undone.
                        </div>
                    </div>

                    <div class="form-card mb-3">
                        <div class="form-card-header">
                            <i class="fas fa-comment-alt me-2 text-danger"></i>
                            <label for="rejection_reason" class="form-label mb-0">Reason for Rejection</label>
                            <span class="required-indicator">*</span>
                        </div>
                        <div class="form-card-body">
                            <textarea class="modern-textarea" id="rejection_reason" name="rejection_reason" rows="3" required placeholder="Please provide a detailed reason for rejecting this transfer..."></textarea>
                            <div class="form-info mt-2">
                                <i class="fas fa-info-circle me-1"></i> This reason will be visible to all parties involved in the transfer.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-soft-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-soft-danger">
                        <i class="fas fa-ban me-1"></i> Reject Transfer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Modern React-like UI Styles */
:root {
    --primary: #2E7D32;
    --primary-light: #4CAF50;
    --primary-dark: #1B5E20;
    --primary-bg: rgba(46, 125, 50, 0.08);
    --secondary: #607D8B;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --light: #f8f9fa;
    --dark: #1e293b;
    --white: #ffffff;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
}

/* Modern Page Header */
.modern-page-header {
    background-color: var(--white);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

/* Success Toast */
.success-toast {
    animation: fadeInDown 0.4s ease-out forwards;
    background-color: rgba(16, 185, 129, 0.12);
    border-left: 3px solid #10b981;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.success-toast-icon, .info-toast-icon {
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.success-toast-icon {
    color: #10b981;
}

.info-toast-icon {
    color: #3b82f6;
}

.success-toast-content, .info-toast-content {
    flex-grow: 1;
}

.success-toast-close, .info-toast-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    font-size: 0.9rem;
    margin-left: 1rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.success-toast-close:hover, .info-toast-close:hover {
    opacity: 1;
}

/* Alert Cards */
.alert-card {
    display: flex;
    align-items: flex-start;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-card.error {
    background-color: rgba(239, 68, 68, 0.1);
}

.alert-card.warning {
    background-color: rgba(245, 158, 11, 0.1);
}

.alert-card-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.alert-card.error .alert-card-icon {
    background-color: var(--danger);
    color: white;
}

.alert-card.warning .alert-card-icon {
    background-color: var(--warning);
    color: white;
}

.alert-card-content {
    flex-grow: 1;
    font-size: 0.9rem;
}

.alert-card.error .alert-card-content {
    color: var(--danger);
}

.alert-card.warning .alert-card-content {
    color: var(--warning);
}

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    background-color: var(--gray-200);
    color: var(--gray-700);
}

.status-badge.pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-badge.approved-by-logistics,
.status-badge.approved-by-himu {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.status-badge.completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-badge.rejected {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Modern Cards */
.modern-card {
    background-color: var(--white);
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.modern-card-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-100);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-icon-bg {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background-color: var(--primary-bg);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.modern-card-body {
    padding: 1.5rem;
}

/* Transaction Code */
.transaction-code {
    display: inline-flex;
    align-items: center;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Info Cards */
.info-card {
    background-color: var(--white);
    border-radius: 12px;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
}

.info-card:hover {
    border-color: var(--primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.info-card-header {
    background-color: var(--gray-50);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--dark);
}

.info-card-body {
    padding: 1rem;
}

.info-item {
    display: flex;
    margin-bottom: 0.75rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    width: 120px;
    color: var(--gray-500);
    font-size: 0.9rem;
    padding-top: 0.25rem;
}

.info-value {
    flex: 1;
}

/* Location Badge */
.location-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.location-badge.source {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.location-badge.destination {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

/* User Badge */
.user-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
    margin-right: 0.5rem;
}

.user-badge.logistics {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.user-badge.himu {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.user-badge.received {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.timestamp {
    font-size: 0.8rem;
    color: var(--gray-500);
    white-space: nowrap;
}

/* Notes Content */
.notes-content {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.9rem;
    color: var(--gray-700);
    line-height: 1.5;
}

.notes-content.rejection {
    background-color: rgba(239, 68, 68, 0.05);
    color: var(--danger);
}

.rejection-card .info-card-header {
    color: var(--danger);
}

/* Horizontal Timeline */
.horizontal-timeline {
    position: relative;
    padding: 1rem 0;
    margin: 0 auto;
}

.timeline-progress-bar {
    position: relative;
    height: 4px;
    background-color: var(--gray-200);
    border-radius: 4px;
    margin: 0 auto 2rem;
    width: 90%;
    max-width: 1000px;
}

.progress-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background-color: var(--gray-200);
    border-radius: 4px;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: var(--success);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.timeline-steps {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 90%;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.timeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    flex: 1;
    max-width: 200px;
    min-width: 120px;
    padding: 0 10px;
}

.step-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--gray-200);
    border: 3px solid var(--white);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.timeline-step.completed .step-icon {
    background-color: var(--success);
    color: white;
    transform: scale(1.05);
}

.timeline-step.rejected .step-icon {
    background-color: var(--danger);
    color: white;
}

.step-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.timeline-step.completed .step-label {
    color: var(--success);
}

.timeline-step.rejected .step-label {
    color: var(--danger);
}

.step-details {
    font-size: 0.8rem;
    color: var(--gray-500);
    max-width: 100%;
}

.step-date {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.step-user {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.step-pending {
    color: var(--gray-400);
    font-style: italic;
}

.rejection-banner {
    display: flex;
    align-items: flex-start;
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1.5rem;
    width: 90%;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.rejection-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--danger);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.rejection-content {
    flex: 1;
}

.rejection-title {
    font-weight: 600;
    color: var(--danger);
    margin-bottom: 0.5rem;
}

.rejection-reason {
    font-size: 0.9rem;
    color: var(--danger);
    opacity: 0.9;
}

/* Responsive adjustments for timeline */
@media (max-width: 768px) {
    .timeline-steps {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 1rem;
        justify-content: flex-start;
    }

    .timeline-step {
        min-width: 150px;
    }
}

@media (max-width: 576px) {
    .timeline-step {
        min-width: 120px;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-label {
        font-size: 0.8rem;
    }

    .step-details {
        font-size: 0.75rem;
    }
}

/* Modern Tables */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
}

.modern-table thead th {
    background-color: var(--gray-100);
    color: var(--gray-500);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.modern-table thead th:first-child {
    border-top-left-radius: 10px;
}

.modern-table thead th:last-child {
    border-top-right-radius: 10px;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: var(--gray-50);
}

.modern-table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
    font-size: 0.9rem;
}

.item-row {
    transition: all 0.2s ease;
}

.item-row:hover {
    background-color: var(--gray-100);
}

/* Badges and Labels */
.id-badge {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
}

.code-badge {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: monospace;
}

.category-badge {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
}

.quantity-badge {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.item-name {
    font-weight: 500;
    color: var(--dark);
}

.quantity-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* Soft Badges */
.bg-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

/* Form Elements */
.form-card {
    background-color: var(--white);
    border-radius: 12px;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
}

.form-card:hover {
    border-color: var(--primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.form-card-header {
    background-color: var(--gray-50);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
}

.form-card-body {
    padding: 1rem;
}

.required-indicator {
    color: var(--danger);
    margin-left: 0.25rem;
}

.modern-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--dark);
    font-size: 0.9rem;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 100px;
}

.modern-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-bg);
}

.form-info {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
}

/* Modern Modal */
.modern-modal {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Buttons */
.btn-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary:hover {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
    transform: translateY(-1px);
}

.btn-soft-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-secondary:hover {
    background-color: var(--gray-500);
    color: white;
    box-shadow: 0 4px 10px rgba(107, 114, 128, 0.2);
    transform: translateY(-1px);
}

.btn-soft-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-danger:hover {
    background-color: var(--danger);
    color: white;
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.2);
    transform: translateY(-1px);
}

.btn-soft-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-success:hover {
    background-color: var(--success);
    color: white;
    box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2);
    transform: translateY(-1px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-item {
        flex-direction: column;
    }

    .info-label {
        width: 100%;
        margin-bottom: 0.25rem;
    }

    .timeline-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTables
    $('.table').DataTable({
        pageLength: 10
    });

    // Check if we should auto-open the PTR receipt
    <?php if (isset($_GET['show_ptr']) && $_GET['show_ptr'] == 1 && !empty($inventoryItems) && !$hasFixedAssetsWithLocalMR): ?>
    // Auto-open PTR receipt for consumable inventory (only if there are no fixed assets with Local MR receipt type)
    printBatchTransferReceipt();
    <?php endif; ?>

    // Start polling for new notifications
    if (typeof pollNotifications !== 'function') {
        // Define the notification polling function
        function pollNotifications() {
            fetch('/choims/api/check_notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.count > 0) {
                        // Update notification counter in the header
                        const notificationCounter = document.querySelector('.notification-counter');
                        if (notificationCounter) {
                            notificationCounter.textContent = data.count;
                            notificationCounter.style.display = 'inline-block';
                        }

                        // Optionally show a toast notification for the newest notification
                        if (data.newest && !sessionStorage.getItem('notification_' + data.newest.id)) {
                            showToast(data.newest.title, data.newest.message);
                            sessionStorage.setItem('notification_' + data.newest.id, 'true');
                        }
                    }
                })
                .catch(error => console.error('Error checking notifications:', error));
        }

        // Simple toast notification function
        function showToast(title, message) {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.style.cssText = 'position:fixed;top:20px;right:20px;z-index:9999;';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast show';
            toast.style.cssText = 'min-width:250px;margin-bottom:10px;background-color:#fff;border-radius:4px;box-shadow:0 0 10px rgba(0,0,0,0.2);overflow:hidden;';

            // Create toast header
            const toastHeader = document.createElement('div');
            toastHeader.className = 'toast-header';
            toastHeader.style.cssText = 'padding:10px;background-color:#f8f9fa;border-bottom:1px solid #dee2e6;display:flex;justify-content:space-between;align-items:center;';

            // Create title and close button
            const titleEl = document.createElement('strong');
            titleEl.textContent = title;

            const closeButton = document.createElement('button');
            closeButton.type = 'button';
            closeButton.className = 'btn-close';
            closeButton.setAttribute('data-bs-dismiss', 'toast');
            closeButton.style.cssText = 'background:none;border:none;font-size:16px;cursor:pointer;';
            closeButton.textContent = '×';
            closeButton.onclick = function() { toast.remove(); };

            toastHeader.appendChild(titleEl);
            toastHeader.appendChild(closeButton);

            // Create toast body
            const toastBody = document.createElement('div');
            toastBody.className = 'toast-body';
            toastBody.style.cssText = 'padding:10px;';
            toastBody.textContent = message;

            // Assemble toast
            toast.appendChild(toastHeader);
            toast.appendChild(toastBody);

            // Add to container
            toastContainer.appendChild(toast);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        // Poll every 30 seconds
        pollNotifications(); // Initial check
        setInterval(pollNotifications, 30000);
    }
});

// Function to print batch transfer receipt as PTR
function printBatchTransferReceipt() {
    // Get current date formatted
    const today = new Date();
    const todayDate = today.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Get transfer data from PHP variables
    const batchId = '<?php echo $batch_id; ?>';
    const transactionCode = '<?php echo isset($batch['transaction_code']) ? $batch['transaction_code'] : "N/A"; ?>';
    const sourceLocation = '<?php echo addslashes($batch['source_location']); ?>';
    const destinationLocation = '<?php echo addslashes($batch['destination_location']); ?>';
    const transferDate = '<?php echo date('F j, Y', strtotime($batch['transfer_date'])); ?>';
    const receivedBy = '<?php echo !empty($batch['received_by_name']) ? addslashes($batch['received_by_name']) : ""; ?>';
    const receivedDate = '<?php echo !empty($batch['received_date']) ? date('F j, Y', strtotime($batch['received_date'])) : ""; ?>';
    const initiatedBy = '<?php echo !empty($batch['initiated_by_name']) ? addslashes($batch['initiated_by_name']) : ""; ?>';
    const notes = '<?php echo !empty($batch['transfer_notes']) ? addslashes(nl2br($batch['transfer_notes'])) : ""; ?>';

    // Get asset and inventory items as arrays
    const assets = <?php echo json_encode($assets); ?>;
    const inventory = <?php echo json_encode($inventoryItems); ?>;

    // Create a new window for the PTR document
    const ptrWindow = window.open('', '_blank', 'width=800,height=600');
    ptrWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Property Transfer Report (PTR)</title>
            <style>
                @page {
                    size: A4 portrait;
                    margin: 0;
                }
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 10px;
                    max-height: 148mm; /* Half of A4 height */
                    overflow: hidden;
                }
                .document-header { display: flex; align-items: center; justify-content: flex-start; margin-bottom: 10px; }
                .header-text { text-align: left; flex: 1; }
                .header-text h1 { margin: 0; font-size: 18px; text-transform: uppercase; font-weight: bold; }
                .header-text h2, .header-text h3 { margin: 0; font-size: 16px; text-transform: uppercase; font-weight: bold; }
                .header-text p { margin: 0; font-size: 14px; }
                .logo-container { text-align: left; }
                .logo-container img { height: 50px; width: 50px; }
                .logo-container .date-printed { text-align: center; font-size: 12px; margin-top: 5px; }
                .main-content { padding: 0; margin-bottom: 10px; }
                .info-section { margin-bottom: 15px; }
                .info-row { display: flex; margin-bottom: 5px; }
                .info-label { width: 120px; font-weight: bold; }
                .info-data { flex: 1; }
                .header-separator { border-top: 1px solid #000; margin: 10px 0; }
                .item-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .item-table th, .item-table td { border: 1px solid #000; padding: 8px; text-align: left; }
                .item-table th { background-color: #f2f2f2; }
                .signatures { display: flex; justify-content: space-between; margin-top: 20px; }
                .signature-block { width: 45%; text-align: center; }
                .signaturess { display: flex; justify-content: space-between; margin-top: 10px; margin-bottom: 0; }
                .signature-blockss { width: 45%; text-align: center; }
                .signature-line { width: 100%; border-top: 1px solid #000; margin-bottom: 0; padding-top: 0; }
                .signature-block div { margin-top: -2px; line-height: 1.2; }
                .footer { margin-top: 15px; font-size: 12px; text-align: center; }
                .print-btn {
                    padding: 8px 16px;
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                }
                .print-btn:hover {
                    background-color: #45a049;
                }
                @media print {
                    body {
                        padding: 0;
                        margin: 0;
                    }
                    button { display: none; }
                    @page {
                        size: A4 portrait;
                        margin: 0;
                    }
                    /* Hide browser-generated content */
                    html {
                        height: 148mm; /* Half of A4 height */
                        overflow: hidden;
                    }
                }
            </style>
        </head>
        <body>
            <div class="main-content">
                <div class="document-header">
                    <div class="logo-container">
                        <img src="/choims/assets/img/prqlogo2.png" alt="Hospital Logo" style="height: 60px; width: auto;">
                    </div>
                    <div class="header-text" style="margin-top: 12px; margin-left: 10px;">
                        <h1 style="margin-bottom: 0;">OFFICE OF THE CITY HEALTH OFFICER</h1>
                        <h5 style="margin-top: 0; font-weight: normal;">PROPERTY TRANSFER REPORT (PTR)</h5>
                    </div>
                </div>

                <div class="header-separator"></div>

                <div class="info-section" style="padding-top: 10px; padding-bottom: 5px;">
                    <div class="info-row d-flex justify-content-between">
                        <div style="width: 45%;">
                            <span class="info-label">FROM:</span>
                            <span class="info-data">${sourceLocation}</span>
                        </div>
                        <div style="width: 45%;">
                            <span class="info-label">TO:</span>
                            <span class="info-data">${destinationLocation}</span>
                        </div>
                    </div>

                </div>

                <table class="item-table">
                    <thead>
                        <tr>
                            <th style="width: 5%;">NO.</th>
                            <th style="width: 50%;">ITEM DESCRIPTION</th>
                            <th style="width: 15%;">TRANSACTION CODE</th>
                            <th style="width: 15%;">TRANSFER DATE</th>
                            <th style="width: 15%;">QTY</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${inventory.map((item, index) => `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${item.sku_name} (${item.sku_code})</td>
                                <td>${transactionCode || 'N/A'}</td>
                                <td>${transferDate}</td>
                                <td>${item.quantity} ${item.unit_of_measure || ''}</td>
                            </tr>
                        `).join('')}

                        ${assets.map((asset, index) => `
                            <tr>
                                <td>${inventory.length + index + 1}</td>
                                <td>${asset.asset_name} (${asset.sku_code}) - Serial: ${asset.serial_number || 'N/A'}</td>
                                <td>${transactionCode || 'N/A'}</td>
                                <td>${transferDate}</td>
                                <td>1 unit</td>
                            </tr>
                        `).join('')}

                        ${inventory.length === 0 && assets.length === 0 ? `
                            <tr>
                                <td colspan="5" style="text-align: center; font-style: italic;">No items in this transfer</td>
                            </tr>
                        ` : ''}

                        <tr>
                            <td colspan="5" style="text-align: center; font-style: italic;">-Nothing Follows-</td>
                        </tr>
                    </tbody>
                </table>

                ${notes ? `
                <div style="margin-bottom: 20px;">
                    <strong>TRANSFER NOTES:</strong>
                    <p>${notes}</p>
                </div>
                ` : ''}

                <div class="signaturess" style="margin-top: 20px; margin-bottom: 0;">
                    <div class="signature-block">
                        <div><strong>ISSUED BY:</strong></div>
                    </div>
                    <div class="signature-blockss">
                        <div><strong>RECEIVED BY:</strong></div>
                    </div>
                </div>

                <div class="signatures" style="margin-top: 70px; margin-bottom: 10px;">
                    <div class="signature-block">
                        <div class="signature-line"></div>
                        <div style="margin-top: 0;"><small>Signature over Printed Name / Date Issued</small></div>
                        <div><strong>${initiatedBy || '___________________'}</strong></div>
                    </div>
                    <div class="signature-block">
                        <div class="signature-line"></div>
                        <div style="margin-top: 1px;"><small>Signature over Printed Name / Date Received</small></div>
                        <div><strong>${receivedBy || '___________________'}</strong></div>
                        <div>${receivedDate || ''}</div>
                    </div>
                </div>
            </div>

            <div class="footer" style="margin-top: 20px; text-align: center;">
                <button class="print-btn" style="padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2); transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#3d9c40'; this.style.boxShadow='0 6px 10px rgba(76, 175, 80, 0.3)'; this.style.transform='translateY(-2px)';" onmouseout="this.style.backgroundColor='#4CAF50'; this.style.boxShadow='0 4px 6px rgba(76, 175, 80, 0.2)'; this.style.transform='translateY(0)';" onclick="window.print()">
                    <i class="fas fa-print" style="margin-right: 8px;"></i> Print PTR Document
                </button>
            </div>
        </body>
        </html>
    `);
    ptrWindow.document.close();
}
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>